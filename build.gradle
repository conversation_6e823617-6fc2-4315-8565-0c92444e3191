buildscript {
    repositories {
        mavenCentral()
    }
    dependencies {
        classpath "org.rodnansol:spring-configuration-property-documenter-gradle-plugin:${springConfigurationPropertyDocumenterVersion}"
    }
}

plugins {
    id "org.springframework.boot" version "${springBootVersion}" apply false
    id "io.spring.dependency-management" version "${springDependencyManagementVersion}" apply false
    id "com.diffplug.spotless" version "${spotlessVersion}" apply false
    id "com.github.spotbugs" version "${spotbugsVersion}" apply false
    id "com.google.protobuf" version "${protobufGradlePluginVersion}" apply false
    id "org.graalvm.buildtools.native" version "${graalvmBuildToolsVersion}" apply false
    id "org.jreleaser" version "${jReleaserVersion}" apply false
    id "io.spring.nullability" version "${nullabilityVersion}" apply false
}

allprojects {
    if (it.name == "grpc-starter-dependencies") {
        // skip the bom project
        return
    }

    apply plugin: "java"
    apply plugin: "java-library"

    sourceSets {
        optionalSupport
    }

    java {
        registerFeature("optionalSupport") {
            usingSourceSet(sourceSets.optionalSupport)
        }
    }

    javadoc {
        // Suppress warnings about missing Javadoc comments
        options.addStringOption('Xdoclint:none', '-quiet')
    }

    repositories {
        mavenLocal()
        mavenCentral()
        maven { url = "https://repo.spring.io/snapshot" }
        maven { url = "https://repo.spring.io/milestone" }
    }

    compileJava {
        options.encoding = "UTF-8"
        options.compilerArgs << "-parameters"
    }
    compileTestJava {
        options.encoding = "UTF-8"
        options.compilerArgs << "-parameters"
    }

    test {
        systemProperties("spring.cloud.compatibility-verifier.enabled": "false")
        useJUnitPlatform()

        jvmArgs("-XX:+EnableDynamicAgentLoading")
    }

    afterEvaluate {
        tasks.findByName("bootRun")?.configure {
            systemProperty("spring.cloud.compatibility-verifier.enabled", "false")
        }
    }

    // dependency management
    apply plugin: "io.spring.dependency-management"
    dependencyManagement {
        imports {
            mavenBom "org.springframework.boot:spring-boot-dependencies:${springBootVersion}"
            mavenBom "com.google.protobuf:protobuf-bom:${protobufVersion}"
            mavenBom "io.grpc:grpc-bom:${grpcVersion}"
        }
    }

    dependencies {
        compileOnly("org.projectlombok:lombok")
        annotationProcessor("org.projectlombok:lombok")
        testCompileOnly("org.projectlombok:lombok")
        testAnnotationProcessor("org.projectlombok:lombok")
        compileOnly("com.github.spotbugs:spotbugs-annotations:${spotbugsAnnotationsVersion}")
        // https://docs.gradle.org/current/userguide/upgrading_version_8.html#test_framework_implementation_dependencies
        testRuntimeOnly("org.junit.platform:junit-platform-launcher")
    }

    // spotless
    apply plugin: "com.diffplug.spotless"
    spotless {
        encoding "UTF-8"
        java {
            toggleOffOn()
            removeUnusedImports()
            trimTrailingWhitespace()
            endWithNewline()
            palantirJavaFormat()

            targetExclude "**/generated/**"

            custom("Refuse wildcard imports", {
                if (it =~ /\nimport .*\*;/) {
                    throw new IllegalStateException("Do not use wildcard imports, 'spotlessApply' cannot resolve this issue, please fix it manually.")
                }
            } as Closure<String>)
        }
    }

    // spotbugs
    apply plugin: "com.github.spotbugs"
    spotbugs {
        spotbugsTest.enabled = false
        omitVisitors.addAll "FindReturnRef", "DontReusePublicIdentifiers"
        excludeFilter = file("${rootDir}/config/spotbugs/exclude.xml")
    }

    apply plugin: "io.spring.nullability"
}

apply from: "${rootDir}/gradle/generate-configuration-properties-docs.gradle"
apply from: "${rootDir}/gradle/jreleaser.gradle"
