---
sidebar_position: 10
---

import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

# gRPC HTTP Transcoding

This extension provides a Java implementation of [gRPC HTTP transcoding](https://cloud.google.com/endpoints/docs/grpc/transcoding),
similar to [grpc-gateway](https://github.com/grpc-ecosystem/grpc-gateway) in Go.

Refer to the [google.api.http](https://github.com/googleapis/googleapis/blob/master/google/api/http.proto) specification
to understand how to define HTTP mappings.

## Why Not Use a Sidecar?

Why not use a sidecar like [Envoy gRPC-JSON transcoder](https://www.envoyproxy.io/docs/envoy/latest/configuration/http/http_filters/grpc_json_transcoder_filter)
for transcoding?

- A sidecar acts as a black box, making debugging and testing inconvenient.
- It lacks sufficient scalability, and customizing transcoding is challenging.

## Dependencies

Select the dependencies that match your technology stack.

<Tabs>
    <TabItem value="webmvc" label="WebMVC">
        <Tabs>
            <TabItem value="gradle" label="Gradle">
                ```groovy
                implementation("io.github.danielliu1123:grpc-server-boot-starter")
                implementation("io.github.danielliu1123:grpc-starter-transcoding")
                implementation("org.springframework.boot:spring-boot-starter-web")
                ```
            </TabItem>
            <TabItem value="maven" label="Maven">
                ```xml
                <dependency>
                    <groupId>io.github.danielliu1123</groupId>
                    <artifactId>grpc-server-boot-starter</artifactId>
                </dependency>
                <dependency>
                    <groupId>io.github.danielliu1123</groupId>
                    <artifactId>grpc-starter-transcoding</artifactId>
                </dependency>
                <dependency>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-web</artifactId>
                </dependency>
                ```
            </TabItem>
        </Tabs>
    </TabItem>
    <TabItem value="webflux" label="WebFlux">
        <Tabs>
            <TabItem value="gradle" label="Gradle">
                ```groovy
                implementation("io.github.danielliu1123:grpc-server-boot-starter")){
                    exclude(group: "io.grpc", module: "grpc-netty-shaded")
                }
                implementation("io.github.danielliu1123:grpc-starter-transcoding")
                implementation("org.springframework.boot:spring-boot-starter-webflux")
                runtimeOnly("io.grpc:grpc-netty")
                ```
            </TabItem>
            <TabItem value="maven" label="Maven">
                ```xml
                <dependency>
                    <groupId>io.github.danielliu1123</groupId>
                    <artifactId>grpc-server-boot-starter</artifactId>
                    <exclusions>
                        <exclusion>
                            <groupId>io.grpc</groupId>
                            <artifactId>grpc-netty-shaded</artifactId>
                        </exclusion>
                    </exclusions>
                </dependency>
                <dependency>
                    <groupId>io.github.danielliu1123</groupId>
                    <artifactId>grpc-starter-transcoding</artifactId>
                </dependency>
                <dependency>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-webflux</artifactId>
                </dependency>
                <dependency>
                    <groupId>io.grpc</groupId>
                    <artifactId>grpc-netty</artifactId>
                    <scope>runtime</scope>
                </dependency>
                ```
            </TabItem>
        </Tabs>
    </TabItem>
</Tabs>

:::tip
Since both WebFlux and gRPC utilize Netty, the grpc-starter now (>=3.6.0) uses `grpc-netty` by default instead of `grpc-netty-shaded` to avoid increasing the size of the final JAR package by approximately 9MB.
If you need to use `grpc-netty-shaded` for specific requirements, you can exclude `grpc-netty` and add `grpc-netty-shaded` as a dependency.
:::

## Example

Transcoding is only support [unary](#unary) and [server streaming](#server-streaming) RPCs.

### Unary

```protobuf
syntax = "proto3";

import "google/api/annotations.proto";

package transcoding.mvc;

message SimpleRequest {
    string requestMessage = 1;
}
message SimpleResponse {
    string responseMessage = 1;
}
service SimpleService {
    rpc UnaryRpc (SimpleRequest) returns (SimpleResponse) {
        option (google.api.http) = {
            post: "/unary",
            body: "*"
        };
    }
}
```

**HTTP:**

```shell
curl -X POST -d '{"requestMessage": "World"}' http://localhost:8080/unary
```

**gRPC:**

```shell
grpcurl -plaintext -d '{"requestMessage": "World"}' localhost:9090 transcoding.mvc.SimpleService/UnaryRpc
```

:::info
When `grpc.transcoding.auto-mapping` is `true`(default value) and `google.api.http` option is not specified,
the default mapping path is `POST /<package>.<Service>/<MethodName>` (case-sensitive).
:::

Refer to [webmvc example](https://github.com/DanielLiu1123/grpc-starter/tree/main/examples/transcoding/webmvc).

### Server Streaming

It is implemented using [SSE](https://html.spec.whatwg.org/multipage/server-sent-events.html) (Server-Sent Events),
data will be returned in JSON format.

```protobuf
syntax = "proto3";

import "google/api/annotations.proto";

package transcoding.mvc;

message SimpleRequest {
    string requestMessage = 1;
}
message SimpleResponse {
    string responseMessage = 1;
}
service SimpleService {
    rpc ServerStreamingRpc (SimpleRequest) returns (stream SimpleResponse) {
        option (google.api.http) = {
            get: "/serverstreaming"
        };
    }
}
```

**HTTP:**

```shell
curl http://localhost:8080/serverstreaming?requestMessage=World
```

**gRPC:**

```shell
grpcurl -plaintext -d '{"requestMessage": "World"}' localhost:9090 transcoding.mvc.SimpleService/ServerStreamingRpc
```

Refer to [webmvc example](https://github.com/DanielLiu1123/grpc-starter/tree/main/examples/transcoding/webmvc).

:::info
The values of request message fields can be filled by path variables, request body and query parameters.
If the same field exists, the priority is: **path variables > request body > query parameters**.
:::

## OpenAPI Integration

**Available since version [*******](https://github.com/DanielLiu1123/grpc-starter/releases/tag/v*******)**

The `grpc-starter-transcoding-springdoc` module provides automatic OpenAPI documentation generation for your gRPC transcoding endpoints. This integration allows you to:

- Automatically generate OpenAPI 3.0 specifications for transcoded gRPC services
- Access interactive Swagger UI for testing your APIs
- Leverage SpringDoc's powerful documentation features

:::tip
The integration leverages [springdoc-bridge-protobuf](https://github.com/DanielLiu1123/springdoc-bridge) to handle protobuf message conversion to OpenAPI schemas, ensuring accurate documentation generation.
:::

### Dependencies

To enable OpenAPI integration, use the `grpc-starter-transcoding-springdoc` dependency instead of the basic transcoding module:

<Tabs>
    <TabItem value="gradle" label="Gradle">
        ```groovy
        implementation("io.github.danielliu1123:grpc-server-boot-starter")
        implementation("io.github.danielliu1123:grpc-starter-transcoding-springdoc")
        implementation("org.springframework.boot:spring-boot-starter-web")
        implementation("org.springdoc:springdoc-openapi-starter-webmvc-ui")
        ```
    </TabItem>
    <TabItem value="maven" label="Maven">
        ```xml
        <dependency>
            <groupId>io.github.danielliu1123</groupId>
            <artifactId>grpc-server-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.danielliu1123</groupId>
            <artifactId>grpc-starter-transcoding-springdoc</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
        </dependency>
        ```
    </TabItem>
</Tabs>

### Configuration

The OpenAPI integration works automatically with minimal configuration. You can customize the behavior using standard SpringDoc properties:

```yaml
springdoc:
  api-docs:
    enabled: true
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
  use-fqn: true  # Use fully qualified names for better clarity
```

### Usage

Once configured, the integration automatically:

1. **Scans your gRPC services** for HTTP transcoding annotations (`google.api.http`)
2. **Generates OpenAPI paths** based on the HTTP mappings
3. **Creates request/response schemas** from your protobuf message definitions
4. **Handles path parameters, query parameters, and request bodies** according to your gRPC service definitions

After starting your application, you can access:

- **OpenAPI JSON**: `http://localhost:8080/v3/api-docs`
- **Swagger UI**: `http://localhost:8080/swagger-ui.html`

### Example

Refer to the [examples/transcoding-springdoc](https://github.com/DanielLiu1123/grpc-starter/tree/main/examples/transcoding-springdoc) for a complete working demonstration.

## Extension Points

### HeaderConverter

`HeaderConverter` is used to
- Convert HTTP headers to gRPC metadata when transcode from HTTP to gRPC.
- Convert gRPC metadata to HTTP headers when transcode from gRPC to HTTP.

The default implementation is `DefaultHeaderConverter`.
- When converting HTTP headers to gRPC metadata (request), it removes all http canonical headers (except Authorization) and leaves only custom headers.
- When converting gRPC metadata to HTTP headers (response), it removes all http canonical headers, headers with the prefix `grpc-`, headers with the suffix `-bin` and leaves only custom headers.

Use custom `HeaderConverter` to replace the default implementation:

```java
@Component
public class CustomHeaderConverter implements HeaderConverter {

    @Override
    public Metadata toMetadata(HttpHeaders headers) {
        // convert HttpHeaders to Metadata
    }

    @Override
    public HttpHeaders toHttpHeaders(Metadata headers) {
        // convert Metadata to HttpHeaders
    }
}
```

### Custom Transcoding Exception Handling

Provides two interfaces [`TranscodingExceptionResolver`](#transcodingexceptionresolver) (Spring MVC)
and [`ReactiveTranscodingExceptionHandler`](#reactivetranscodingexceptionhandler) (Spring WebFlux) for custom transcoding exception handling.

The default implementations are [`DefaultTranscodingExceptionResolver`](https://github.com/DanielLiu1123/grpc-starter/blob/main/grpc-extensions/grpc-transcoding/src/main/java/grpcstarter/extensions/transcoding/DefaultTranscodingExceptionResolver.java)
and [`DefaultReactiveTranscodingExceptionHandler`](https://github.com/DanielLiu1123/grpc-starter/blob/main/grpc-extensions/grpc-transcoding/src/main/java/grpcstarter/extensions/transcoding/DefaultReactiveTranscodingExceptionResolver.java).

The logic of the default implementation is:
when an exception occurs, gRPC status code will be converted to http status code,
and then throw `TranscodingRuntimeException`,
which will then be captured and handled by Spring MVC/Flux.

:::tip
If you want Spring to handle `ResponseStatusException`,
you need to set `spring.{mvc,webflux}.problemdetails.enabled` to `true`.
:::

#### TranscodingExceptionResolver

Customize a `TranscodingExceptionResolver` to replace the default implementation:

```java
@Component
public class MyTranscodingExceptionResolver implements TranscodingExceptionResolver {

    @Override
    public ServerResponse resolve(StatusRuntimeException exception) {
        // do your custom logic here ...
    }
}
```

:::warning
Spring MVC does not handle `ResponseStatusException` thrown by `HandlerFunction` before Spring `6.2.0`.
Refer to [spring-projects/spring-framework#32689](https://github.com/spring-projects/spring-framework/issues/32689).
:::

#### ReactiveTranscodingExceptionHandler

Customize a `ReactiveTranscodingExceptionHandler` to replace the default implementation:

```java
public class MyReactiveTranscodingExceptionResolver implements ReactiveTranscodingExceptionResolver {

    @Override
    public void resolve(MonoSink<ServerResponse> sink, StatusRuntimeException exception) {
        // do your custom logic here ...

        // return a exception response
        // sink.error(new MyException());

        // return a normal response
        // ServerResponse.ok().bodyValue("Ops!").subscribe(sink::success);
    }
}
```

## Configurations

Disable transcoding:

```yaml
grpc:
  transcoding:
    enabled: false
```

Disable auto-mapping:

```yaml
grpc:
  transcoding:
    auto-mapping: false
```

Custom gRPC server endpoint:

```yaml
grpc:
  transcoding:
    endpoint: localhost:9090
```

:::note
In most cases, you do not need to manually configure this property,
it will automatically find the gRPC server endpoint of the current application.
:::

## How It Works

1. During application startup, all protobuf configurations for HTTP mapping (google.api.http) are retrieved 
and registered into the internally implemented [`RouterFunction`](https://docs.spring.io/spring-framework/reference/web/webmvc-functional.html#webmvc-fn-overview).

2. When an HTTP request is received, the `RouterFunction` identifies the corresponding gRPC service based on the request’s URL and method. 
It then converts the HTTP request into a gRPC request, sends it to the gRPC server endpoint, and finally converts the gRPC response back into an HTTP response to return to the client.

For more details, refer to [grpc-gateway](https://github.com/grpc-ecosystem/grpc-gateway?tab=readme-ov-file#about).
