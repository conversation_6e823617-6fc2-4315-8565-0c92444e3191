---
sidebar_position: 50
---

# Version

The `grpc-starter-dependencies` project simplifies dependency management for gRPC and Protobuf by aligning its versions with Spring Boot. 
This ensures compatibility and reduces the need for manual version management.

| Spring Boot    | grpc-starter-dependencies                                                                                                                                                                                            | grpc-java | protobuf-java |
|----------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-----------|---------------|
| 3.6.x          | [![Maven <PERSON>](https://img.shields.io/maven-central/v/io.github.danielliu1123/grpc-starter-dependencies?versionPrefix=3.6.)](https://search.maven.org/artifact/io.github.danielliu1123/grpc-starter-dependencies) | 1.73.0    | 4.31.1        |
| 3.5.x          | [![Maven Central](https://img.shields.io/maven-central/v/io.github.danielliu1123/grpc-starter-dependencies?versionPrefix=3.5.)](https://search.maven.org/artifact/io.github.danielliu1123/grpc-starter-dependencies) | 1.72.0    | 3.25.5        |
| 3.4.x          | [![Maven Central](https://img.shields.io/maven-central/v/io.github.danielliu1123/grpc-starter-dependencies?versionPrefix=3.4.)](https://search.maven.org/artifact/io.github.danielliu1123/grpc-starter-dependencies) | 1.68.3    | 3.25.5        |
| 3.3.x          | [![Maven Central](https://img.shields.io/maven-central/v/io.github.danielliu1123/grpc-starter-dependencies?versionPrefix=3.3.)](https://search.maven.org/artifact/io.github.danielliu1123/grpc-starter-dependencies) | 1.68.1    | 3.25.5        |
| 3.2.x          | [![Maven Central](https://img.shields.io/maven-central/v/io.github.danielliu1123/grpc-starter-dependencies?versionPrefix=3.2.)](https://search.maven.org/artifact/io.github.danielliu1123/grpc-starter-dependencies) | 1.62.2    | 3.25.1        |
| 3.1.x          | [![Maven Central](https://img.shields.io/maven-central/v/com.freemanan/grpc-starter-dependencies?versionPrefix=3.1.)](https://search.maven.org/artifact/com.freemanan/grpc-starter-dependencies)                     | 1.59.1    | 3.25.1        |
| 2.x (>= 2.4.0) | [![Maven Central](https://img.shields.io/maven-central/v/com.freemanan/grpc-starter-dependencies?versionPrefix=2.)](https://search.maven.org/artifact/com.freemanan/grpc-starter-dependencies)                       | 1.59.1    | 3.25.1        |

To find the specific versions of `grpc-java` and `protobuf-java` used by `grpc-starter-dependencies`, 
visit the [grpc-starter-dependencies](https://central.sonatype.com/artifact/io.github.danielliu1123/grpc-starter-dependencies) page.

:::tip
The `grpc-starter-dependencies` version is kept in sync with the latest Spring Boot releases. 
We monitor Spring Boot updates daily and release corresponding versions to ensure compatibility.
:::

## Important Release Notes

### 3.6.x

- **Protobuf 4.x Upgrade**: Upgraded from Protobuf 3.x to 4.x.
- **protovalidate 0.12.0**
- **protoc-gen-validate 1.2.1**

### *******

- **Native Image Support**: Support native image compilation with GraalVM for all modules.

### *******

- **Native Image Support**: Support native image compilation with GraalVM for core modules (client and server).
  - Refer to [native-image example](https://github.com/DanielLiu1123/grpc-starter/tree/main/examples/native-image)

### 3.3.1

- **Dependency Removal**: The `javax.annotation:javax.annotation-api` dependency has been removed.
- **Configuration Guide**: Instructions on configuring gRPC to avoid generating the `javax.annotation.Generated` annotation:
  - [Maven Configuration](https://github.com/DanielLiu1123/grpc-starter-maven-demo/blob/main/pom.xml#L92)
  - [Gradle Configuration](https://github.com/DanielLiu1123/grpc-starter/blob/main/gradle/protobuf.gradle#L15)
  - [Buf Configuration](https://github.com/DanielLiu1123/grpc-starter/blob/main/examples/transcoding/best-practice/bp-api/buf.gen.yaml#L7)

### 3.2.0

- **Group ID Change**: The groupId has been updated from `com.freemanan` to `io.github.danielliu1123`.
