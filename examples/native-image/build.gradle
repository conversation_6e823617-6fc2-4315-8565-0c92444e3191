plugins {
    id "org.springframework.boot"
    id "org.graalvm.buildtools.native"
}

dependencies {
    // grpc-netty is now used by default, which supports GraalVM native-image
    implementation(project(":grpc-starters:grpc-boot-starter"))

    implementation("io.grpc:grpc-testing-proto")

    testImplementation(project(":grpc-starters:grpc-starter-test"))
}

// https://graalvm.github.io/native-build-tools/latest/gradle-plugin.html#configuration-options
graalvmNative {
    testSupport = false
    binaries {
        main {
            verbose = true
            sharedLibrary = false
        }
    }
}
