plugins {
    id "org.springframework.boot"
    id "org.graalvm.buildtools.native"
}

dependencies {
    implementation(project(":grpc-starters:grpc-boot-starter"))
    implementation(project(":grpc-starters:grpc-starter-validation"))

    implementation("io.grpc:grpc-testing-proto")

    testImplementation(project(":grpc-starters:grpc-starter-test"))
}

apply from: "${rootDir}/gradle/protobuf-with-pgv.gradle"

// https://graalvm.github.io/native-build-tools/latest/gradle-plugin.html#configuration-options
graalvmNative {
    testSupport = false
    binaries {
        main {
            verbose = true
            sharedLibrary = false
        }
    }
}
