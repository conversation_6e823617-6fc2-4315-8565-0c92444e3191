dependencies {
    api(project(":grpc-boot-autoconfigure:grpc-server-boot-autoconfigure"))
    runtimeOnly("io.grpc:grpc-netty")

    optionalSupportApi("io.grpc:grpc-netty-shaded")

    // health check support
    optionalSupportApi("org.springframework.boot:spring-boot-starter-jdbc")
    optionalSupportApi("org.springframework.boot:spring-boot-starter-data-redis")

    testImplementation(project(":grpc-starters:grpc-starter-test"))
    testImplementation("io.grpc:grpc-testing-proto")
}

apply from: "${rootDir}/gradle/deploy.gradle"
